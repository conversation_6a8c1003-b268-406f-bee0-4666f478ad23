import React, { useState } from 'react';
import { useThemeStore } from '../../stores/themeStore';
import {
  authService,
  type LoginCredentials,
  type User,
} from '../../services/authService';
import { Button, Input, Card, Text, Heading } from '../ui';

export interface LoginDemoProps {
  onLoginSuccess?: (user: User) => void;
  className?: string;
  'data-testid'?: string;
}

const LoginDemo: React.FC<LoginDemoProps> = ({
  onLoginSuccess,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [credentials, setCredentials] = useState<LoginCredentials>({
    email: '',
    password: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<User | null>(
    authService.getCurrentUser()
  );

  const handleInputChange =
    (field: keyof LoginCredentials) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setCredentials(prev => ({
        ...prev,
        [field]: e.target.value,
      }));
      setError(null);
    };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.login(credentials);
      setCurrentUser(response.user);
      onLoginSuccess?.(response.user);
      console.log('Login successful:', response);
    } catch (err: any) {
      setError(err.message || 'Login failed');
      console.error('Login error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      await authService.logout();
      setCurrentUser(null);
      setCredentials({ email: '', password: '' });
      console.log('Logout successful');
    } catch (err: any) {
      console.error('Logout error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const quickLogin = async (email: string, password: string) => {
    setCredentials({ email, password });
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.login({ email, password });
      setCurrentUser(response.user);
      onLoginSuccess?.(response.user);
      console.log('Quick login successful:', response);
    } catch (err: any) {
      setError(err.message || 'Login failed');
      console.error('Quick login error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  if (currentUser) {
    return (
      <Card
        variant="elevated"
        padding="lg"
        className={className}
        data-testid={testId}
      >
        <div className="text-center space-y-4">
          <div className="text-4xl">{currentUser.avatar}</div>
          <div>
            <Heading level={4} className="mb-2">
              Welcome, {currentUser.name}!
            </Heading>
            <Text variant="body" color="secondary">
              {currentUser.email} • {currentUser.role}
            </Text>
          </div>

          <div className="space-y-2">
            <div className="flex flex-wrap gap-1 justify-center">
              {currentUser.permissions.map(permission => (
                <span
                  key={permission}
                  className="px-2 py-1 text-xs rounded-full"
                  style={{
                    backgroundColor: colors.muted,
                    color: colors.mutedForeground,
                  }}
                >
                  {permission}
                </span>
              ))}
            </div>
          </div>

          <Button
            variant="outline"
            onClick={handleLogout}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? 'Logging out...' : 'Logout'}
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card
      variant="elevated"
      padding="lg"
      className={className}
      data-testid={testId}
    >
      <div className="space-y-6">
        <div className="text-center">
          <Heading level={4} className="mb-2">
            Login Demo
          </Heading>
          <Text variant="body" color="secondary">
            Test the mock authentication service
          </Text>
        </div>

        <form onSubmit={handleLogin} className="space-y-4">
          <Input
            label="Email"
            type="email"
            value={credentials.email}
            onChange={handleInputChange('email')}
            placeholder="Enter email or use quick login"
            required
            disabled={isLoading}
          />

          <Input
            label="Password"
            type="password"
            value={credentials.password}
            onChange={handleInputChange('password')}
            placeholder="Enter password"
            required
            disabled={isLoading}
          />

          {error && (
            <div
              className="p-3 rounded-lg text-sm"
              style={{
                backgroundColor: colors.destructive + '20',
                color: colors.destructive,
                border: `1px solid ${colors.destructive}40`,
              }}
            >
              {error}
            </div>
          )}

          <Button
            type="submit"
            variant="primary"
            disabled={isLoading || !credentials.email || !credentials.password}
            className="w-full"
          >
            {isLoading ? 'Logging in...' : 'Login'}
          </Button>
        </form>

        <div className="space-y-3">
          <Text variant="caption" color="secondary" className="text-center">
            Quick Login Options:
          </Text>

          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => quickLogin('<EMAIL>', 'admin123')}
              disabled={isLoading}
            >
              Admin
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => quickLogin('<EMAIL>', 'manager123')}
              disabled={isLoading}
            >
              Manager
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => quickLogin('<EMAIL>', 'user123')}
              disabled={isLoading}
            >
              User
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => quickLogin('demo', 'demo')}
              disabled={isLoading}
            >
              Demo
            </Button>
          </div>
        </div>

        <div
          className="text-xs space-y-1"
          style={{ color: colors.mutedForeground }}
        >
          <Text variant="caption" color="secondary">
            Available credentials:
          </Text>
          <div className="font-mono text-xs space-y-1">
            <div><EMAIL> / admin123</div>
            <div><EMAIL> / manager123</div>
            <div><EMAIL> / user123</div>
            <div>demo / demo</div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default LoginDemo;
