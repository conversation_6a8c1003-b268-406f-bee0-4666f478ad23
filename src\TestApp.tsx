export default function TestApp() {
  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-4">
          Test App Working!
        </h1>
        <p className="text-slate-600 dark:text-slate-400">
          React Router and SWR implementation is ready.
        </p>
        <div className="mt-8 space-y-4">
          <a
            href="/demo"
            className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go to Demo Page
          </a>
          <div>
            <a
              href="/"
              className="text-blue-600 hover:text-blue-800 transition-colors"
            >
              Back to Login
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
