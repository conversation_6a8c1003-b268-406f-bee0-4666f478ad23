import React, { useState } from 'react';
import { CenteredSearchChipInput } from '../components/ui';
import type {
  FilterTag,
  FilterItem,
  GroupByItem,
  FavoriteItem,
} from '../components/ui/CenteredSearchChipInput/CenteredSearchChipInput';
import { useThemeStore } from '../stores/themeStore';

const CenteredSearchDemo: React.FC = () => {
  const { colors } = useThemeStore();
  const [filterTags, setFilterTags] = useState<FilterTag[]>([
    { id: '1', label: 'Undelivered Complete', removable: true, type: 'filter' },
    { id: '2', label: 'Order', removable: true, type: 'filter' },
    { id: '3', label: 'Salesperson', removable: true, type: 'groupBy' },
    { id: '4', label: 'My Favorites', removable: true, type: 'favorite' },
  ]);

  const filterItems: FilterItem[] = [
    { id: 'status', label: 'Status' },
    { id: 'priority', label: 'Priority' },
    { id: 'department', label: 'Department' },
    { id: 'date-range', label: 'Date Range', hasDropdown: true },
    { id: 'my-quotations', label: 'My Quotations' },
    { id: 'quotations', label: 'Quotations' },
    { id: 'sales-orders', label: 'Sales Orders' },
    { id: 'create-date', label: 'Create Date', hasDropdown: true },
  ];

  const groupByItems: GroupByItem[] = [
    { id: 'group-salesperson', label: 'Salesperson' },
    { id: 'group-customer', label: 'Customer' },
    { id: 'group-order-date', label: 'Order Date', hasDropdown: true },
    { id: 'group-region', label: 'Region' },
  ];

  const favoriteItems: FavoriteItem[] = [
    { id: 'fav-fully-invoiced', label: 'Fully Invoiced' },
    { id: 'fav-quotations', label: 'Quotations' },
    { id: 'fav-undelivered-complete', label: 'Undelivered Complete' },
    { id: 'fav-unpaid-orders', label: 'Unpaid Orders' },
  ];

  const handleTagRemove = (tagId: string) => {
    setFilterTags(prev => prev.filter(tag => tag.id !== tagId));
  };

  const handleFilterSelect = (filterId: string) => {
    console.log('Filter selected:', filterId);
    // Add logic to add filter as tag
    const filterItem = filterItems.find(item => item.id === filterId);
    if (filterItem) {
      const newTag: FilterTag = {
        id: Date.now().toString(),
        label: filterItem.label,
        removable: true,
        type: 'filter',
      };
      setFilterTags(prev => [...prev, newTag]);
    }
  };

  const handleGroupBySelect = (groupId: string) => {
    console.log('Group by selected:', groupId);
  };

  const handleFavoriteSelect = (favoriteId: string) => {
    console.log('Favorite selected:', favoriteId);
  };

  const handleFavoriteDelete = (favoriteId: string) => {
    console.log('Favorite deleted:', favoriteId);
  };

  const handleAddCustomFilter = () => {
    console.log('Add custom filter');
  };

  const handleAddCustomGroup = () => {
    console.log('Add custom group');
  };

  const handleSaveCurrentSearch = () => {
    console.log('Save current search');
  };

  const handleSearch = (query: string) => {
    console.log('Search query:', query);
  };

  return (
    <div
      className="min-h-screen p-8"
      style={{ backgroundColor: colors.background }}
    >
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1
            className="text-3xl font-bold mb-2"
            style={{ color: colors.text }}
          >
            Centered Search Chip Input Demo
          </h1>
          <p className="text-lg" style={{ color: colors.textSecondary }}>
            A comprehensive search input with integrated filter chips,
            supporting default filters, custom filters, grouping, and favorites.
          </p>
        </div>

        {/* Main Demo */}
        <div className="mb-12">
          <h2
            className="text-xl font-semibold mb-4"
            style={{ color: colors.text }}
          >
            Interactive Demo
          </h2>
          <CenteredSearchChipInput
            placeholder="Search..."
            filterTags={filterTags}
            filterItems={filterItems}
            groupByItems={groupByItems}
            favoriteItems={favoriteItems}
            onTagRemove={handleTagRemove}
            onFilterSelect={handleFilterSelect}
            onGroupBySelect={handleGroupBySelect}
            onFavoriteSelect={handleFavoriteSelect}
            onFavoriteDelete={handleFavoriteDelete}
            onAddCustomFilter={handleAddCustomFilter}
            onAddCustomGroup={handleAddCustomGroup}
            onSaveCurrentSearch={handleSaveCurrentSearch}
            onSearch={handleSearch}
          />
        </div>

        {/* Features Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {[
            {
              title: 'Default Filters',
              description:
                'Pre-defined filters like Status, Priority, Department, and Date Range',
              icon: '🔍',
            },
            {
              title: 'Custom Filters',
              description:
                'User-defined custom fields and filters for specific needs',
              icon: '⚙️',
            },
            {
              title: 'Group By',
              description:
                'Organize data by Salesperson, Customer, Order Date, or custom groups',
              icon: '📊',
            },
            {
              title: 'Favorites',
              description:
                'Quick access to frequently used searches and saved filters',
              icon: '⭐',
            },
          ].map((feature, index) => (
            <div
              key={index}
              className="p-6 rounded-lg border"
              style={{
                backgroundColor: colors.surface,
                borderColor: colors.border,
              }}
            >
              <div className="text-2xl mb-3">{feature.icon}</div>
              <h3
                className="text-lg font-semibold mb-2"
                style={{ color: colors.text }}
              >
                {feature.title}
              </h3>
              <p className="text-sm" style={{ color: colors.textSecondary }}>
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Debug Info */}
        <div
          className="p-6 rounded-lg border"
          style={{
            backgroundColor: colors.surface,
            borderColor: colors.border,
          }}
        >
          <h3
            className="text-lg font-semibold mb-4"
            style={{ color: colors.text }}
          >
            Debug Information
          </h3>
          <div className="space-y-2">
            <p style={{ color: colors.textSecondary }}>
              <strong>Active Chips:</strong> {filterTags.length}
            </p>
            <div>
              <strong style={{ color: colors.textSecondary }}>Chips:</strong>
              <ul className="list-disc list-inside ml-4 mt-1">
                {filterTags.map(tag => (
                  <li key={tag.id} style={{ color: colors.textMuted }}>
                    {tag.label}
                  </li>
                ))}
              </ul>
            </div>
            <p style={{ color: colors.textSecondary }}>
              <strong>Available Filter Options:</strong> {filterItems.length}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CenteredSearchDemo;
